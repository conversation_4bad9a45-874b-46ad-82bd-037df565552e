import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { router } from 'expo-router';
import { User, Role } from '@/types';
import { supabase } from '@/lib/supabase';
import type { Session } from '@supabase/supabase-js';

/**
 * Authentication context interface defining all auth-related state and methods
 */
interface AuthContextType {
  /** Currently authenticated user or null if not authenticated */
  user: User | null;
  /** Whether an authentication operation is in progress */
  isLoading: boolean;
  /** Current authentication error message or null */
  error: string | null;
  /** Function to authenticate a user with email and password */
  login: (email: string, password: string) => Promise<void>;
  /** Function to register a new user */
  register: (userData: Partial<User>, password: string) => Promise<void>;
  /** Function to log out the current user */
  logout: () => void;
  /** Boolean indicating if a user is currently authenticated */
  isAuthenticated: boolean;
}

/**
 * React context for managing authentication state throughout the application
 */
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Authentication provider component that manages user authentication state
 * and provides authentication methods to child components.
 *
 * @param children - React components that will have access to auth context
 * @returns JSX element wrapping children with authentication context
 */
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check for existing session on load and listen for auth changes
  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      setIsLoading(true);
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;

        if (session?.user) {
          await loadUserProfile(session.user.id);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Error getting session:', error);
        setError(error instanceof Error ? error.message : 'Failed to get session');
      } finally {
        setIsLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          await loadUserProfile(session.user.id);
          setIsAuthenticated(true);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setIsAuthenticated(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  /**
   * Loads user profile from the database
   * @param userId - The user's ID from Supabase auth
   */
  const loadUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;

      if (data) {
        const userProfile: User = {
          id: data.id,
          email: data.email,
          firstName: data.first_name,
          lastName: data.last_name,
          role: data.role as Role,
          avatar: data.avatar_url || undefined,
        };
        setUser(userProfile);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      setError(error instanceof Error ? error.message : 'Failed to load user profile');
    }
  };

  /**
   * Authenticates a user with email and password
   * @param email - User's email address
   * @param password - User's password
   * @throws Will throw an error if authentication fails
   */
  const login = async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      if (data.user) {
        await loadUserProfile(data.user.id);
        setIsAuthenticated(true);
        router.replace('/(tabs)/dashboard');
      }
    } catch (e) {
      setError((e as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Registers a new user account
   * @param userData - Partial user data for registration
   * @param password - User's chosen password
   * @throws Will throw an error if registration fails
   */
  const register = async (userData: Partial<User>, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!userData.email) {
        throw new Error('Email is required');
      }

      // Sign up with Supabase Auth
      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password,
      });

      if (error) throw error;

      if (data.user) {
        // Create user profile in the database
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email: userData.email,
            first_name: userData.firstName || '',
            last_name: userData.lastName || '',
            role: userData.role || 'employee',
            avatar_url: userData.avatar || null,
          });

        if (profileError) throw profileError;

        // Load the user profile
        await loadUserProfile(data.user.id);
        setIsAuthenticated(true);
        router.replace('/(tabs)/dashboard');
      }
    } catch (e) {
      setError((e as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Logs out the current user and redirects to login screen
   */
  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      setUser(null);
      setIsAuthenticated(false);
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Error signing out:', error);
      setError(error instanceof Error ? error.message : 'Failed to sign out');
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        error,
        login,
        register,
        logout,
        isAuthenticated,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Custom hook to access authentication context
 * @returns AuthContextType object with user state and auth methods
 * @throws Error if used outside of AuthProvider
 */
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}