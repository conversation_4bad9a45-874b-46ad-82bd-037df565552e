import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';

type Tables = Database['public']['Tables'];

/**
 * Custom hook for querying data from Supabase tables
 * @template T - The table name type
 * @param table - The name of the table to query
 * @param options - Query options including select, filters, ordering, and limits
 * @param options.select - Columns to select (default: '*')
 * @param options.eq - Equality filter with column and value
 * @param options.order - Ordering configuration with column and direction
 * @param options.limit - Maximum number of rows to return
 * @returns Object containing data, error, loading state, and refetch function
 */
export function useQuery<T extends keyof Tables>(
  table: T,
  options: {
    select?: string;
    eq?: { column: string; value: any };
    order?: { column: string; ascending?: boolean };
    limit?: number;
  } = {}
) {
  const [data, setData] = useState<Tables[T]['Row'][]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      let query = supabase.from(table).select(options.select || '*');

      if (options.eq) {
        query = query.eq(options.eq.column, options.eq.value);
      }

      if (options.order) {
        query = query.order(options.order.column, {
          ascending: options.order.ascending ?? true,
        });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data: result, error: queryError } = await query;

      if (queryError) throw queryError;
      setData(result as any || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [table, JSON.stringify(options)]);

  return { data, error, loading, refetch: fetchData };
}

/**
 * Custom hook for performing mutations (insert, update, delete) on Supabase tables
 * @template T - The table name type
 * @param table - The name of the table to perform mutations on
 * @returns Object containing mutation functions (insert, update, remove) and state (loading, error)
 */
export function useMutation<T extends keyof Tables>(table: T) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Inserts a new record into the table
   * @param data - The data to insert
   * @returns The inserted record or null if error occurred
   */
  const insert = async (data: Tables[T]['Insert']) => {
    setLoading(true);
    setError(null);
    try {
      const { data: result, error: insertError } = await supabase
        .from(table)
        .insert(data)
        .select()
        .single();

      if (insertError) throw insertError;
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Updates an existing record in the table
   * @param id - The ID of the record to update
   * @param data - The data to update
   * @returns The updated record or null if error occurred
   */
  const update = async (id: string, data: Tables[T]['Update']) => {
    setLoading(true);
    setError(null);
    try {
      const { data: result, error: updateError } = await supabase
        .from(table)
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (updateError) throw updateError;
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return null;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Removes a record from the table
   * @param id - The ID of the record to remove
   * @returns True if successful, false if error occurred
   */
  const remove = async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const { error: deleteError } = await supabase
        .from(table)
        .delete()
        .eq('id', id);

      if (deleteError) throw deleteError;
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    insert,
    update,
    remove,
    loading,
    error,
  };
}