/**
 * Authentication and User Types
 */

/** Available user roles in the system */
export type Role = 'admin' | 'manager' | 'employee' | 'client';

/** User entity representing a system user */
export interface User {
  /** Unique identifier for the user */
  id: string;
  /** User's email address (used for authentication) */
  email: string;
  /** User's first name */
  firstName: string;
  /** User's last name */
  lastName: string;
  /** User's role determining permissions */
  role: Role;
  /** Optional avatar image URL */
  avatar?: string;
}

/**
 * Client Management Types
 */

/** Client entity representing a customer or prospect */
export interface Client {
  /** Unique identifier for the client */
  id: string;
  /** C<PERSON>'s business or full name */
  name: string;
  /** Primary email address */
  email: string;
  /** Primary phone number */
  phone: string;
  /** Street address */
  address: string;
  /** City */
  city: string;
  /** State or province */
  state: string;
  /** ZIP or postal code */
  zipCode: string;
  /** Current client status */
  status: 'active' | 'inactive' | 'prospect';
  /** Optional notes about the client */
  notes?: string;
  /** Timestamp when client was created */
  createdAt: string;
  /** Timestamp when client was last updated */
  updatedAt: string;
}

/**
 * Estimate and Project Types
 */

/** Individual line item within an estimate */
export interface EstimateItem {
  /** Unique identifier for the item */
  id: string;
  /** Name/title of the item */
  name: string;
  /** Detailed description of the work or material */
  description: string;
  /** Quantity of the item */
  quantity: number;
  /** Unit of measurement (e.g., 'sq ft', 'linear ft', 'each') */
  unit: string;
  /** Price per unit */
  unitPrice: number;
  /** Total price for this item (quantity × unitPrice) */
  total: number;
}

/** Complete estimate for a construction project */
export interface Estimate {
  /** Unique identifier for the estimate */
  id: string;
  /** Title/name of the estimate */
  title: string;
  /** ID of the associated client */
  clientId: string;
  /** Name of the associated client (for display) */
  clientName: string;
  /** Current status of the estimate */
  status: 'draft' | 'sent' | 'approved' | 'declined' | 'expired';
  /** Array of line items in the estimate */
  items: EstimateItem[];
  /** Subtotal before tax */
  subtotal: number;
  /** Tax rate as decimal (e.g., 0.0825 for 8.25%) */
  taxRate: number;
  /** Calculated tax amount */
  taxAmount: number;
  /** Final total including tax */
  total: number;
  /** Optional notes or terms */
  notes?: string;
  /** ID of the user who created the estimate */
  createdBy: string;
  /** Timestamp when estimate was created */
  createdAt: string;
  /** Timestamp when estimate was last updated */
  updatedAt: string;
  /** Timestamp when estimate expires */
  expiresAt: string;
}

/**
 * Template Types
 */

/** Reusable estimate template for common project types */
export interface Template {
  /** Unique identifier for the template */
  id: string;
  /** Name of the template */
  name: string;
  /** Category for organizing templates */
  category: string;
  /** Description of what the template is for */
  description: string;
  /** Array of default items included in the template */
  items: EstimateItem[];
  /** ID of the user who created the template */
  createdBy: string;
  /** Timestamp when template was created */
  createdAt: string;
  /** Timestamp when template was last updated */
  updatedAt: string;
}

/**
 * Dashboard and Activity Types
 */

/** Dashboard statistics and metrics */
export interface DashboardStats {
  /** Total number of estimates in the system */
  totalEstimates: number;
  /** Number of estimates pending approval */
  pendingApprovals: number;
  /** Number of active clients */
  activeClients: number;
  /** Recent activity feed */
  recentActivity: Activity[];
}

/** Activity log entry for tracking system events */
export interface Activity {
  /** Unique identifier for the activity */
  id: string;
  /** Type of activity that occurred */
  type: 'estimate_created' | 'estimate_approved' | 'estimate_declined' | 'client_added';
  /** Human-readable title of the activity */
  title: string;
  /** Detailed description of the activity */
  description: string;
  /** Timestamp when the activity occurred */
  date: string;
  /** ID of the user who performed the activity */
  userId: string;
  /** Name of the user who performed the activity */
  userName: string;
}