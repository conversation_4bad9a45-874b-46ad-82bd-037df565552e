/**
 * Data service for interacting with Supabase database
 * Provides typed methods for CRUD operations on all entities
 */

import { supabase } from '@/lib/supabase';
import { Client, Estimate, Template, Activity, DashboardStats } from '@/types';

/**
 * Client data service
 */
export const clientService = {
  /**
   * Get all clients for the current user
   */
  async getAll(): Promise<Client[]> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map(client => ({
      id: client.id,
      name: client.name,
      email: client.email,
      phone: client.phone,
      address: client.address,
      city: client.city,
      state: client.state,
      zipCode: client.zip_code,
      status: client.status,
      notes: client.notes,
      createdAt: client.created_at,
      updatedAt: client.updated_at,
    }));
  },

  /**
   * Get a single client by ID
   */
  async getById(id: string): Promise<Client | null> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    return {
      id: data.id,
      name: data.name,
      email: data.email,
      phone: data.phone,
      address: data.address,
      city: data.city,
      state: data.state,
      zipCode: data.zip_code,
      status: data.status,
      notes: data.notes,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  },

  /**
   * Create a new client
   */
  async create(client: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .insert({
        name: client.name,
        email: client.email,
        phone: client.phone,
        address: client.address,
        city: client.city,
        state: client.state,
        zip_code: client.zipCode,
        status: client.status,
        notes: client.notes,
      })
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      name: data.name,
      email: data.email,
      phone: data.phone,
      address: data.address,
      city: data.city,
      state: data.state,
      zipCode: data.zip_code,
      status: data.status,
      notes: data.notes,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  },

  /**
   * Update an existing client
   */
  async update(id: string, updates: Partial<Omit<Client, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .update({
        ...(updates.name && { name: updates.name }),
        ...(updates.email && { email: updates.email }),
        ...(updates.phone && { phone: updates.phone }),
        ...(updates.address && { address: updates.address }),
        ...(updates.city && { city: updates.city }),
        ...(updates.state && { state: updates.state }),
        ...(updates.zipCode && { zip_code: updates.zipCode }),
        ...(updates.status && { status: updates.status }),
        ...(updates.notes !== undefined && { notes: updates.notes }),
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      name: data.name,
      email: data.email,
      phone: data.phone,
      address: data.address,
      city: data.city,
      state: data.state,
      zipCode: data.zip_code,
      status: data.status,
      notes: data.notes,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  },

  /**
   * Delete a client
   */
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  /**
   * Search clients by name or email
   */
  async search(query: string): Promise<Client[]> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .or(`name.ilike.%${query}%,email.ilike.%${query}%`)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map(client => ({
      id: client.id,
      name: client.name,
      email: client.email,
      phone: client.phone,
      address: client.address,
      city: client.city,
      state: client.state,
      zipCode: client.zip_code,
      status: client.status,
      notes: client.notes,
      createdAt: client.created_at,
      updatedAt: client.updated_at,
    }));
  },
};

/**
 * Estimate data service
 */
export const estimateService = {
  /**
   * Get all estimates for the current user
   */
  async getAll(userId?: string): Promise<Estimate[]> {
    let query = supabase
      .from('estimates')
      .select(`
        *,
        clients(name),
        estimate_items(*)
      `)
      .order('created_at', { ascending: false });

    if (userId) {
      query = query.eq('created_by', userId);
    }

    const { data, error } = await query;

    if (error) throw error;

    return data.map(estimate => ({
      id: estimate.id,
      title: estimate.title,
      clientId: estimate.client_id,
      clientName: estimate.clients?.name || 'Unknown Client',
      status: estimate.status,
      items: estimate.estimate_items?.map((item: any) => ({
        id: item.id,
        name: item.name,
        description: item.description,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: item.unit_price,
        total: item.total,
      })) || [],
      subtotal: estimate.subtotal,
      taxRate: estimate.tax_rate,
      taxAmount: estimate.tax_amount,
      total: estimate.total,
      notes: estimate.notes,
      createdBy: estimate.created_by,
      createdAt: estimate.created_at,
      updatedAt: estimate.updated_at,
      expiresAt: estimate.expires_at,
    }));
  },

  /**
   * Get estimates by status
   */
  async getByStatus(status: string, userId?: string): Promise<Estimate[]> {
    let query = supabase
      .from('estimates')
      .select(`
        *,
        clients(name),
        estimate_items(*)
      `)
      .eq('status', status)
      .order('created_at', { ascending: false });

    if (userId) {
      query = query.eq('created_by', userId);
    }

    const { data, error } = await query;

    if (error) throw error;

    return data.map(estimate => ({
      id: estimate.id,
      title: estimate.title,
      clientId: estimate.client_id,
      clientName: estimate.clients?.name || 'Unknown Client',
      status: estimate.status,
      items: estimate.estimate_items?.map((item: any) => ({
        id: item.id,
        name: item.name,
        description: item.description,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: item.unit_price,
        total: item.total,
      })) || [],
      subtotal: estimate.subtotal,
      taxRate: estimate.tax_rate,
      taxAmount: estimate.tax_amount,
      total: estimate.total,
      notes: estimate.notes,
      createdBy: estimate.created_by,
      createdAt: estimate.created_at,
      updatedAt: estimate.updated_at,
      expiresAt: estimate.expires_at,
    }));
  },
};

/**
 * Dashboard data service
 */
export const dashboardService = {
  /**
   * Get dashboard statistics
   */
  async getStats(userId?: string): Promise<DashboardStats> {
    // Get total estimates count
    let estimatesQuery = supabase
      .from('estimates')
      .select('id', { count: 'exact', head: true });

    if (userId) {
      estimatesQuery = estimatesQuery.eq('created_by', userId);
    }

    // Get pending approvals count
    let pendingQuery = supabase
      .from('estimates')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'sent');

    if (userId) {
      pendingQuery = pendingQuery.eq('created_by', userId);
    }

    // Get active clients count
    const activeClientsQuery = supabase
      .from('clients')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'active');

    const [estimatesResult, pendingResult, clientsResult] = await Promise.all([
      estimatesQuery,
      pendingQuery,
      activeClientsQuery,
    ]);

    if (estimatesResult.error) throw estimatesResult.error;
    if (pendingResult.error) throw pendingResult.error;
    if (clientsResult.error) throw clientsResult.error;

    return {
      totalEstimates: estimatesResult.count || 0,
      pendingApprovals: pendingResult.count || 0,
      activeClients: clientsResult.count || 0,
      recentActivity: [], // TODO: Implement activity tracking
    };
  },
};
