# Component Documentation

This document provides detailed information about the reusable components in the Construction Estimate App.

## Core Components

### Button Component

A versatile button component with multiple variants, sizes, and states.

**Location:** `components/Button.tsx`

**Props:**
- `title` (string): The text to display on the button
- `onPress` (function): Function to call when the button is pressed
- `variant` (optional): Visual variant - 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
- `size` (optional): Button size - 'sm' | 'md' | 'lg'
- `disabled` (optional): Whether the button is disabled
- `loading` (optional): Whether to show loading spinner
- `fullWidth` (optional): Whether the button should take full width
- `style` (optional): Additional styles for the button container
- `textStyle` (optional): Additional styles for the button text
- `leftIcon` (optional): Icon to display on the left side
- `rightIcon` (optional): Icon to display on the right side

**Examples:**
```tsx
// Basic button
<Button title="Save" onPress={handleSave} />

// Primary button with icon
<Button
  title="Create Estimate"
  onPress={handleCreate}
  variant="primary"
  leftIcon={<Plus size={16} />}
/>

// Loading state
<Button
  title="Submitting..."
  onPress={handleSubmit}
  loading={isSubmitting}
  disabled={isSubmitting}
/>

// Danger button
<Button
  title="Delete"
  onPress={handleDelete}
  variant="danger"
  size="sm"
/>
```

### Card Component

A container component for grouping related content with consistent styling.

**Location:** `components/Card.tsx`

**Props:**
- `children` (ReactNode): Content to display inside the card
- `style` (optional): Additional styles for the card container

**Examples:**
```tsx
// Basic card
<Card>
  <Text>Card content</Text>
</Card>

// Card with custom styling
<Card style={{ marginBottom: 16 }}>
  <Text style={styles.title}>Client Information</Text>
  <Text>John Doe Construction</Text>
</Card>
```

### StatusBadge Component

Displays status information with appropriate colors and styling.

**Location:** `components/StatusBadge.tsx`

**Props:**
- `status` (string): The status text to display
- `variant` (optional): Color variant - 'success' | 'warning' | 'error' | 'info' | 'default'

**Examples:**
```tsx
// Success status
<StatusBadge status="Approved" variant="success" />

// Warning status
<StatusBadge status="Pending" variant="warning" />

// Error status
<StatusBadge status="Declined" variant="error" />
```

### TextField Component

Input field component with validation, theming, and accessibility features.

**Location:** `components/TextField.tsx`

**Props:**
- `label` (optional): Label text for the input
- `value` (string): Current value of the input
- `onChangeText` (function): Function called when text changes
- `placeholder` (optional): Placeholder text
- `required` (optional): Whether the field is required
- `error` (optional): Error message to display
- `multiline` (optional): Whether to allow multiple lines
- `keyboardType` (optional): Keyboard type for the input
- `secureTextEntry` (optional): Whether to hide text (for passwords)
- `style` (optional): Additional styles for the container

**Examples:**
```tsx
// Basic text field
<TextField
  label="Client Name"
  value={clientName}
  onChangeText={setClientName}
  placeholder="Enter client name"
/>

// Required field with validation
<TextField
  label="Email Address"
  value={email}
  onChangeText={setEmail}
  placeholder="Enter email"
  required
  error={emailError}
  keyboardType="email-address"
/>

// Multiline text area
<TextField
  label="Notes"
  value={notes}
  onChangeText={setNotes}
  placeholder="Enter notes..."
  multiline
  style={{ height: 100 }}
/>
```

### StatCard Component

Displays statistical information with icons and formatting.

**Location:** `components/StatCard.tsx`

**Props:**
- `title` (string): Title of the statistic
- `value` (string | number): The statistical value
- `icon` (optional): Icon to display
- `color` (optional): Color theme for the card
- `onPress` (optional): Function to call when card is pressed

**Examples:**
```tsx
// Basic stat card
<StatCard
  title="Total Estimates"
  value={18}
  icon={<FileText size={24} />}
  color="primary"
/>

// Clickable stat card
<StatCard
  title="Pending Approvals"
  value={5}
  icon={<Clock size={24} />}
  color="warning"
  onPress={() => router.push('/estimates?status=pending')}
/>
```

## Usage Guidelines

### Theming

All components automatically adapt to the current theme (light/dark mode) using the `useTheme` hook. Colors and styles are consistent across the app.

### Accessibility

Components include proper accessibility features:
- Semantic labels and roles
- Screen reader support
- Keyboard navigation
- Touch target sizing

### Performance

Components are optimized for performance:
- Memoization where appropriate
- Efficient re-rendering
- Minimal prop drilling

### Styling

Components use a consistent design system:
- Standardized spacing and typography
- Consistent color palette
- Responsive design principles
- Platform-specific adaptations

## Best Practices

1. **Composition over Configuration**: Prefer composing simple components rather than creating complex ones with many props.

2. **Consistent Naming**: Use descriptive names that clearly indicate the component's purpose.

3. **TypeScript**: All components are fully typed with comprehensive interfaces.

4. **Documentation**: Each component includes JSDoc comments with examples.

5. **Testing**: Components should be testable in isolation with clear interfaces.

6. **Reusability**: Design components to be reusable across different contexts.
