/**
 * Test script to verify Supabase integration
 * Run with: node scripts/test-supabase.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please check your .env file contains:');
  console.log('EXPO_PUBLIC_SUPABASE_URL=your-supabase-url');
  console.log('EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase connection...\n');

  try {
    // Test 1: Basic connection
    console.log('1. Testing basic connection...');
    const { data, error } = await supabase.from('users').select('count', { count: 'exact', head: true });
    
    if (error) {
      console.log('❌ Connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Connection successful');
    console.log(`   Users table exists with ${data || 0} records\n`);

    // Test 2: Check all required tables
    console.log('2. Checking required tables...');
    const tables = ['users', 'clients', 'estimates', 'estimate_items', 'templates', 'template_items'];
    
    for (const table of tables) {
      try {
        const { error: tableError } = await supabase.from(table).select('count', { count: 'exact', head: true });
        if (tableError) {
          console.log(`❌ Table '${table}' not found or accessible`);
          return false;
        }
        console.log(`✅ Table '${table}' exists`);
      } catch (err) {
        console.log(`❌ Error checking table '${table}':`, err.message);
        return false;
      }
    }
    console.log('');

    // Test 3: Check RLS policies
    console.log('3. Testing Row Level Security...');
    try {
      // This should fail without authentication (which is good!)
      const { error: rlsError } = await supabase.from('clients').select('*').limit(1);
      
      if (rlsError && rlsError.message.includes('row-level security')) {
        console.log('✅ Row Level Security is properly configured');
      } else if (rlsError) {
        console.log('⚠️  RLS might not be configured correctly:', rlsError.message);
      } else {
        console.log('⚠️  RLS might be too permissive (no authentication required)');
      }
    } catch (err) {
      console.log('❌ Error testing RLS:', err.message);
    }
    console.log('');

    // Test 4: Test authentication (sign up)
    console.log('4. Testing authentication...');
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'testpassword123';
    
    try {
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
      });

      if (authError) {
        console.log('❌ Authentication test failed:', authError.message);
        return false;
      }

      console.log('✅ Authentication system is working');
      
      // Clean up test user
      if (authData.user) {
        await supabase.auth.signOut();
        console.log('✅ Test user cleaned up');
      }
    } catch (err) {
      console.log('❌ Authentication error:', err.message);
      return false;
    }
    console.log('');

    console.log('🎉 All tests passed! Supabase is properly configured.\n');
    console.log('Next steps:');
    console.log('1. Start your development server: npm run dev');
    console.log('2. Try registering a new user in the app');
    console.log('3. Create some test data (clients, estimates)');
    console.log('4. Check the Supabase dashboard to see your data');
    
    return true;

  } catch (error) {
    console.log('❌ Unexpected error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Construction Estimate App - Supabase Integration Test\n');
  console.log(`Supabase URL: ${supabaseUrl}`);
  console.log(`Anon Key: ${supabaseKey.substring(0, 20)}...\n`);

  const success = await testSupabaseConnection();
  
  if (!success) {
    console.log('\n❌ Some tests failed. Please check your Supabase setup.');
    console.log('Refer to docs/SUPABASE_SETUP.md for detailed setup instructions.');
    process.exit(1);
  }
}

// Run the test
main().catch(console.error);
