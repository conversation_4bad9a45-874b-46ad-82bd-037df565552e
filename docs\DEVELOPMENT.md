# Development Guide

This guide provides detailed information for developers working on the Construction Estimate App.

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Git
- Code editor (VS Code recommended)

### Recommended VS Code Extensions

- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Prettier - Code formatter
- ESLint
- React Native Tools
- Auto Rename Tag
- Bracket Pair Colorizer

### Development Environment Setup

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd construction-estimate-app
   npm install
   ```

2. **Environment Variables**
   Create `.env` file with required variables (see README.md)

3. **Start Development Server**
   ```bash
   npm run dev
   ```

## Project Architecture

### File Structure

```
app/                    # App Router pages (file-based routing)
├── (auth)/            # Authentication flow
├── (tabs)/            # Main app navigation
└── _layout.tsx        # Root layout

components/            # Reusable UI components
├── Button.tsx
├── Card.tsx
└── ...

context/              # React Context providers
├── AuthContext.tsx
└── ThemeContext.tsx

hooks/                # Custom React hooks
├── useSupabase.ts
└── useFrameworkReady.ts

types/                # TypeScript definitions
├── index.ts
└── supabase.ts

lib/                  # External library configurations
└── supabase.ts

data/                 # Mock data and constants
└── mockData.ts
```

### Routing

The app uses Expo Router with file-based routing:

- `app/(auth)/` - Authentication screens
- `app/(tabs)/` - Main app with tab navigation
- `app/(tabs)/dashboard/` - Dashboard screens
- `app/(tabs)/estimates/` - Estimate management
- `app/(tabs)/clients/` - Client management
- `app/(tabs)/templates/` - Template management

### State Management

- **Authentication**: React Context (`AuthContext`)
- **Theme**: React Context (`ThemeContext`)
- **Data Fetching**: Custom hooks (`useQuery`, `useMutation`)
- **Local State**: React useState/useReducer

### Data Flow

1. **Authentication Flow**
   ```
   Login Screen → AuthContext → Supabase Auth → User State → Dashboard
   ```

2. **Data Operations**
   ```
   Component → Custom Hook → Supabase Client → Database → UI Update
   ```

## Coding Standards

### TypeScript

- Use strict TypeScript configuration
- Define interfaces for all data structures
- Use proper typing for props and state
- Avoid `any` type - use proper types or `unknown`

### Component Structure

```tsx
/**
 * Component description with JSDoc
 */
interface ComponentProps {
  /** Prop description */
  propName: PropType;
}

export function ComponentName({ propName }: ComponentProps) {
  // Hooks at the top
  const [state, setState] = useState();
  const { theme } = useTheme();
  
  // Event handlers
  const handleAction = () => {
    // Implementation
  };
  
  // Render
  return (
    <View style={styles.container}>
      {/* JSX */}
    </View>
  );
}

// Styles at the bottom
const styles = StyleSheet.create({
  container: {
    // Styles
  },
});
```

### Naming Conventions

- **Components**: PascalCase (`Button`, `EstimateCard`)
- **Files**: PascalCase for components, camelCase for utilities
- **Variables**: camelCase (`userName`, `isLoading`)
- **Constants**: UPPER_SNAKE_CASE (`API_BASE_URL`)
- **Types/Interfaces**: PascalCase (`User`, `EstimateItem`)

### Import Organization

```tsx
// React imports first
import React, { useState, useEffect } from 'react';

// React Native imports
import { View, Text, StyleSheet } from 'react-native';

// Third-party libraries
import { router } from 'expo-router';

// Local imports (absolute paths)
import { Button } from '@/components/Button';
import { useAuth } from '@/context/AuthContext';
import { User } from '@/types';
```

## Database Integration

### Supabase Setup

The app uses Supabase for backend services:

- **Authentication**: Email/password with role-based access
- **Database**: PostgreSQL with real-time subscriptions
- **Storage**: File uploads (planned)

### Custom Hooks

#### useQuery Hook

```tsx
const { data, error, loading } = useQuery('clients', {
  select: '*',
  eq: { column: 'status', value: 'active' },
  order: { column: 'created_at', ascending: false },
  limit: 10
});
```

#### useMutation Hook

```tsx
const { insert, update, remove, loading, error } = useMutation('estimates');

// Create
const newEstimate = await insert(estimateData);

// Update
const updated = await update(id, updateData);

// Delete
const success = await remove(id);
```

## Testing Strategy

### Unit Testing

- Test individual components in isolation
- Mock external dependencies
- Focus on component behavior and props

### Integration Testing

- Test component interactions
- Test data flow between components
- Test navigation flows

### E2E Testing

- Test complete user workflows
- Test authentication flows
- Test CRUD operations

## Performance Optimization

### React Native Performance

- Use `React.memo` for expensive components
- Implement proper key props for lists
- Optimize image loading and caching
- Use lazy loading for large datasets

### Bundle Optimization

- Tree shaking for unused code
- Code splitting for large features
- Optimize asset sizes
- Use production builds for testing

## Debugging

### Development Tools

- **React Native Debugger**: Component inspection and state
- **Flipper**: Network requests and database queries
- **Expo Dev Tools**: Built-in debugging features
- **Console Logging**: Strategic logging for debugging

### Common Issues

1. **Metro bundler issues**: Clear cache with `npx expo start -c`
2. **TypeScript errors**: Run `npx tsc --noEmit` for type checking
3. **Supabase connection**: Check environment variables and network
4. **Navigation issues**: Verify route structure and parameters

## Deployment

### Development Builds

```bash
# Web preview
npm run build:web

# Mobile preview
npx expo start
```

### Production Builds

```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Configure builds
eas build:configure

# Build for platforms
eas build --platform ios
eas build --platform android
eas build --platform web
```

## Contributing

### Pull Request Process

1. Create feature branch from `main`
2. Implement changes with tests
3. Update documentation
4. Submit PR with clear description
5. Address review feedback
6. Merge after approval

### Code Review Checklist

- [ ] TypeScript types are correct
- [ ] Components are properly documented
- [ ] No console.log statements in production code
- [ ] Proper error handling
- [ ] Accessibility considerations
- [ ] Performance implications considered
- [ ] Tests added/updated
- [ ] Documentation updated
