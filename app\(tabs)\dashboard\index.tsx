import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Card } from '@/components/Card';
import { StatCard } from '@/components/StatCard';
import { StatusBadge } from '@/components/StatusBadge';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { dashboardService, estimateService, clientService } from '@/services/dataService';
import { Router } from 'expo-router';
import { FileText, Users, Clock, CircleCheck as CheckCircle, Calendar, Bell, RefreshCw } from 'lucide-react-native';
import { DashboardStats, Estimate, Client } from '@/types';

/**
 * Dashboard screen component that displays an overview of the user's data
 * including statistics, recent estimates, activity feed, and client information.
 *
 * Features:
 * - Welcome message with current date
 * - Statistics cards with key metrics
 * - Recent estimates list
 * - Activity feed showing recent actions
 * - Recent clients overview
 *
 * @returns JSX.Element - The dashboard screen
 */
export default function DashboardScreen() {
  // Get theme colors for consistent styling across light/dark modes
  const { colors } = useTheme();
  // Get current authenticated user for personalization
  const { user } = useAuth();

  // State for dashboard data
  const [stats, setStats] = useState<DashboardStats>({
    totalEstimates: 0,
    pendingApprovals: 0,
    activeClients: 0,
    recentActivity: [],
  });
  const [recentEstimates, setRecentEstimates] = useState<Estimate[]>([]);
  const [recentClients, setRecentClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * Formats a date string into a short, readable format
   * @param dateString - ISO date string to format
   * @returns Formatted date string (e.g., "Jan 15")
   */
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  /**
   * Returns the appropriate icon component for different activity types
   * @param type - The type of activity (estimate_created, estimate_approved, etc.)
   * @returns React component representing the activity icon
   */
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'estimate_created':
        return <FileText size={16} color={colors.primary} />;
      case 'estimate_approved':
        return <CheckCircle size={16} color={colors.success} />;
      case 'estimate_declined':
        return <Clock size={16} color={colors.error} />;
      case 'client_added':
        return <Users size={16} color={colors.secondary} />;
      default:
        return <Bell size={16} color={colors.textSecondary} />;
    }
  };

  /**
   * Fetches all dashboard data from the API
   */
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch data in parallel for better performance
      const [statsData, estimatesData, clientsData] = await Promise.all([
        dashboardService.getStats(user?.id),
        estimateService.getAll(user?.id),
        clientService.getAll(),
      ]);

      setStats(statsData);
      setRecentEstimates(estimatesData.slice(0, 3)); // Show only 3 most recent
      setRecentClients(clientsData.slice(0, 3)); // Show only 3 most recent
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount and when user changes
  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={styles.contentContainer}
    >
      {/* Welcome Section - Personalized greeting with current date */}
      <View style={styles.welcomeSection}>
        <Text style={[styles.welcomeText, { color: colors.text }]}>
          Welcome back, {user?.firstName || 'User'}!
        </Text>
        <Text style={[styles.dateText, { color: colors.textSecondary }]}>
          {new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
        </Text>
      </View>

      {/* Statistics Section - Horizontal scrollable cards showing key metrics */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.statsContainer}
        style={styles.statsScrollView}
      >
        {/* Total Estimates Card */}
        <StatCard
          title="Total Estimates"
          value={stats.totalEstimates}
          icon={<FileText size={20} color={colors.primary} />}
          style={{ marginRight: 12 }}
        />
        {/* Pending Approvals Card */}
        <StatCard
          title="Pending Approvals"
          value={stats.pendingApprovals}
          icon={<Clock size={20} color={colors.warning} />}
          style={{ marginRight: 12 }}
        />
        {/* Active Clients Card */}
        <StatCard
          title="Active Clients"
          value={stats.activeClients}
          icon={<Users size={20} color={colors.secondary} />}
        />
      </ScrollView>

      {/* Recent Estimates Section - Shows the 3 most recent estimates */}
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Estimates</Text>
        <TouchableOpacity>
          <Text style={[styles.viewAllText, { color: colors.primary }]}>View All</Text>
        </TouchableOpacity>
      </View>

      {/* Show loading state or error */}
      {loading ? (
        <Card style={styles.estimateCard}>
          <View style={styles.loadingContainer}>
            <RefreshCw size={20} color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading estimates...
            </Text>
          </View>
        </Card>
      ) : error ? (
        <Card style={styles.estimateCard}>
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: colors.error }]}>
              {error}
            </Text>
            <TouchableOpacity onPress={fetchDashboardData} style={styles.retryButton}>
              <Text style={[styles.retryText, { color: colors.primary }]}>
                Retry
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      ) : recentEstimates.length === 0 ? (
        <Card style={styles.estimateCard}>
          <View style={styles.emptyContainer}>
            <FileText size={24} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No estimates yet. Create your first estimate to get started!
            </Text>
          </View>
        </Card>
      ) : (
        /* Map through recent estimates and display them as cards */
        recentEstimates.map((estimate) => (
          <Card key={estimate.id} style={styles.estimateCard} onPress={() => { }}>
            <View style={styles.estimateHeader}>
              <Text style={[styles.estimateTitle, { color: colors.text }]} numberOfLines={1}>
                {estimate.title}
              </Text>
              <StatusBadge status={estimate.status} />
            </View>

            <View style={styles.estimateDetails}>
              <View style={styles.estimateDetail}>
                <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Client:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>{estimate.clientName}</Text>
              </View>
              <View style={styles.estimateDetail}>
                <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Total:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  ${estimate.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </Text>
              </View>
              <View style={styles.estimateDetail}>
                <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Created:</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  {formatDate(estimate.createdAt)}
                </Text>
              </View>
            </View>
          </Card>
        ))
      )}

      {/* Recent Activity Section */}
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Activity</Text>
      </View>

      <Card style={styles.activityCard}>
        {stats.recentActivity.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Bell size={24} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No recent activity
            </Text>
          </View>
        ) : (
          stats.recentActivity.map((activity) => (
            <View key={activity.id} style={styles.activityItem}>
              <View style={[styles.activityIcon, { backgroundColor: colors.primaryLight }]}>
                {getActivityIcon(activity.type)}
              </View>
              <View style={styles.activityContent}>
                <Text style={[styles.activityTitle, { color: colors.text }]}>
                  {activity.title}
                </Text>
                <Text style={[styles.activityDescription, { color: colors.textSecondary }]}>
                  {activity.description}
                </Text>
              </View>
              <Text style={[styles.activityDate, { color: colors.textSecondary }]}>
                {formatDate(activity.date)}
              </Text>
            </View>
          ))
        )}
      </Card>

      {/* Recent Clients Section */}
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Clients</Text>
        <TouchableOpacity>
          <Text style={[styles.viewAllText, { color: colors.primary }]}>View All</Text>
        </TouchableOpacity>
      </View>

      {recentClients.length === 0 ? (
        <Card style={styles.clientCard}>
          <View style={styles.emptyContainer}>
            <Users size={24} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No clients yet. Add your first client to get started!
            </Text>
          </View>
        </Card>
      ) : (
        recentClients.map((client) => (
          <Card key={client.id} style={styles.clientCard} onPress={() => { }}>
            <View style={styles.clientHeader}>
              <Text style={[styles.clientName, { color: colors.text }]}>{client.name}</Text>
              <StatusBadge status={client.status} />
            </View>

            <View style={styles.clientDetails}>
              <Text style={[styles.clientDetail, { color: colors.textSecondary }]}>
                {client.email} • {client.phone}
              </Text>
              <Text style={[styles.clientDetail, { color: colors.textSecondary }]}>
                {client.city}, {client.state}
              </Text>
            </View>
          </Card>
        ))
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  welcomeSection: {
    marginBottom: 24,
  },
  welcomeText: {
    fontFamily: 'Inter-Bold',
    fontSize: 24,
  },
  dateText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginTop: 4,
  },
  statsScrollView: {
    marginBottom: 24,
  },
  statsContainer: {
    paddingRight: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
  },
  viewAllText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  estimateCard: {
    marginBottom: 12,
  },
  estimateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  estimateTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    flex: 1,
    marginRight: 8,
  },
  estimateDetails: {
    gap: 4,
  },
  estimateDetail: {
    flexDirection: 'row',
  },
  detailLabel: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    marginRight: 4,
  },
  detailValue: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
  activityCard: {
    marginBottom: 24,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  activityDescription: {
    fontFamily: 'Inter-Regular',
    fontSize: 13,
  },
  activityDate: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
  },
  clientCard: {
    marginBottom: 12,
  },
  clientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  clientName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  clientDetails: {
    gap: 4,
  },
  clientDetail: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginLeft: 8,
  },
  errorContainer: {
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  retryText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
});