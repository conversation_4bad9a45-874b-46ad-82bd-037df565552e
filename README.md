# Construction Estimate App

A comprehensive React Native application built with Expo for managing construction estimates, clients, and project templates. This app provides contractors and construction companies with tools to create professional estimates, manage client relationships, and streamline their project workflow.

## 🚀 Features

### Core Functionality
- **📊 Dashboard** - Real-time overview of estimates, clients, and recent activity
- **👥 Client Management** - Complete CRUD operations for client information
- **📋 Estimate Creation** - Detailed estimate builder with line items and calculations
- **📄 Template System** - Reusable estimate templates for common projects
- **🔐 Authentication** - Secure login/registration with role-based access control
- **🎨 Theme Support** - Light and dark mode with consistent design system

### Technical Features
- **📱 Cross-Platform** - iOS, Android, and Web support via Expo
- **🗄️ Supabase Backend** - Real-time database with authentication
- **🔒 TypeScript** - Full type safety and better developer experience
- **🎯 Navigation** - Tab-based navigation with stack navigation
- **📐 Responsive Design** - Adaptive UI for different screen sizes

## 📋 Table of Contents

- [Installation](#installation)
- [Getting Started](#getting-started)
- [Project Structure](#project-structure)
- [Architecture](#architecture)
- [Environment Setup](#environment-setup)
- [Development](#development)
- [API Documentation](#api-documentation)
- [Components](#components)
- [Contributing](#contributing)
- [License](#license)

## 🛠️ Installation

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Expo CLI
- Git

### Clone the Repository

```bash
git clone https://github.com/yourusername/construction-estimate-app.git
cd construction-estimate-app
```

### Install Dependencies

```bash
npm install
# or
yarn install
```

## 🚀 Getting Started

### Environment Setup

1. Create a `.env` file in the root directory:

```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

2. Set up Supabase:
   - Create a new project at [supabase.com](https://supabase.com)
   - Run the migration file: `supabase/migrations/20250524020959_delicate_summit.sql`
   - Copy your project URL and anon key to the `.env` file
   - **Detailed setup guide**: See [docs/SUPABASE_SETUP.md](docs/SUPABASE_SETUP.md)

3. Test your Supabase connection:
   ```bash
   npm run test:supabase
   ```

### Run the Application

```bash
# Start the development server
npm run dev

# For web development
npm run build:web

# Run linting
npm run lint
```

### Getting Started

1. **Register a new account** in the app or create test users in Supabase
2. **Create sample data** (clients, estimates) to explore the features
3. **Explore the dashboard** to see real-time statistics and data

**Note**: The app now uses real Supabase authentication and database. You'll need to register new users or create them in the Supabase dashboard.

## 📁 Project Structure

```
construction-estimate-app/
├── app/                          # App Router pages
│   ├── (auth)/                   # Authentication screens
│   │   ├── login.tsx
│   │   ├── register.tsx
│   │   └── welcome.tsx
│   ├── (tabs)/                   # Main app tabs
│   │   ├── dashboard/
│   │   ├── clients/
│   │   ├── estimates/
│   │   ├── templates/
│   │   └── settings/
│   └── _layout.tsx               # Root layout
├── components/                   # Reusable UI components
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── StatCard.tsx
│   ├── StatusBadge.tsx
│   └── TextField.tsx
├── context/                      # React Context providers
│   ├── AuthContext.tsx
│   └── ThemeContext.tsx
├── hooks/                        # Custom React hooks
│   ├── useFrameworkReady.ts
│   └── useSupabase.ts
├── lib/                          # External library configurations
│   └── supabase.ts
├── types/                        # TypeScript type definitions
│   ├── index.ts
│   └── supabase.ts
├── data/                         # Mock data and constants
│   └── mockData.ts
├── constants/                    # App constants
│   └── theme.ts
├── supabase/                     # Database migrations
│   └── migrations/
└── assets/                       # Static assets
    └── images/
```

## 🏗️ Architecture

### Technology Stack

- **Frontend**: React Native with Expo
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **Navigation**: Expo Router (file-based routing)
- **State Management**: React Context + Custom Hooks
- **Styling**: React Native StyleSheet with Theme System
- **Type Safety**: TypeScript
- **Icons**: Lucide React Native

### Key Architectural Decisions

1. **File-based Routing**: Using Expo Router for intuitive navigation structure
2. **Context Pattern**: Centralized state management for auth and theme
3. **Custom Hooks**: Reusable logic for Supabase operations
4. **Component Library**: Consistent UI components with theme support
5. **Type-first Development**: Comprehensive TypeScript coverage

### Data Flow

```
User Interaction → Component → Custom Hook → Supabase → Database
                                    ↓
                              Context Update → UI Re-render
```

## 🔧 Environment Setup

### Required Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Optional: Development settings
EXPO_NO_TELEMETRY=1
```

### Supabase Setup

1. **Create a Supabase Project**:
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

2. **Run Database Migrations**:
   ```bash
   # Install Supabase CLI
   npm install -g supabase

   # Initialize Supabase in your project
   supabase init

   # Link to your project
   supabase link --project-ref your-project-ref

   # Run migrations
   supabase db push
   ```

3. **Configure Authentication**:
   - Enable email authentication in Supabase dashboard
   - Configure any additional auth providers as needed

## 💻 Development

### Development Workflow

1. **Start Development Server**:
   ```bash
   npm run dev
   ```

2. **Open on Device**:
   - Scan QR code with Expo Go app (iOS/Android)
   - Press 'w' to open in web browser
   - Press 'i' to open iOS simulator
   - Press 'a' to open Android emulator

3. **Code Quality**:
   ```bash
   # Run TypeScript checks
   npx tsc --noEmit

   # Run linting
   npm run lint

   # Format code (if prettier is configured)
   npx prettier --write .
   ```

### Debugging

- **React Native Debugger**: Use for debugging React components and state
- **Flipper**: For network requests and database queries
- **Expo Dev Tools**: Built-in debugging tools
- **Console Logs**: Check terminal and browser console for logs

### Testing

```bash
# Run tests (when test suite is added)
npm test

# Run tests in watch mode
npm test -- --watch

# Generate coverage report
npm test -- --coverage
```

## 📚 API Documentation

### Authentication Context

The `AuthContext` provides authentication state and methods throughout the app.

```typescript
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: Partial<User>, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}
```

### Custom Hooks

#### useQuery Hook

For fetching data from Supabase:

```typescript
const { data, error, loading, refetch } = useQuery('clients', {
  select: '*',
  eq: { column: 'status', value: 'active' },
  order: { column: 'created_at', ascending: false },
  limit: 10
});
```

#### useMutation Hook

For creating, updating, and deleting data:

```typescript
const { insert, update, remove, loading, error } = useMutation('estimates');

// Create new estimate
const newEstimate = await insert({
  title: 'Kitchen Renovation',
  client_id: 'client-123',
  status: 'draft'
});
```

### Database Schema

#### Users Table
```sql
users (
  id: uuid PRIMARY KEY,
  email: text UNIQUE,
  first_name: text,
  last_name: text,
  role: text,
  avatar_url: text,
  created_at: timestamp,
  updated_at: timestamp
)
```

#### Clients Table
```sql
clients (
  id: uuid PRIMARY KEY,
  name: text,
  email: text,
  phone: text,
  address: text,
  city: text,
  state: text,
  zip_code: text,
  status: text,
  notes: text,
  created_at: timestamp,
  updated_at: timestamp
)
```

#### Estimates Table
```sql
estimates (
  id: uuid PRIMARY KEY,
  title: text,
  client_id: uuid REFERENCES clients(id),
  status: text,
  items: jsonb,
  subtotal: decimal,
  tax_rate: decimal,
  tax_amount: decimal,
  total: decimal,
  notes: text,
  created_by: uuid REFERENCES users(id),
  created_at: timestamp,
  updated_at: timestamp,
  expires_at: timestamp
)
```

## 🧩 Components

### Core Components

#### Button Component

A versatile button component with multiple variants and states.

```typescript
<Button
  title="Create Estimate"
  onPress={handleCreate}
  variant="primary"
  size="lg"
  loading={isLoading}
  leftIcon={<Plus size={16} />}
/>
```

**Props:**
- `title`: Button text
- `onPress`: Click handler
- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
- `size`: 'sm' | 'md' | 'lg'
- `disabled`: Boolean
- `loading`: Boolean
- `fullWidth`: Boolean
- `leftIcon`/`rightIcon`: React components

#### Card Component

Container component for grouping related content.

```typescript
<Card style={styles.container}>
  <Text>Card content</Text>
</Card>
```

#### StatusBadge Component

Displays status with appropriate colors.

```typescript
<StatusBadge
  status="approved"
  variant="success"
/>
```

#### TextField Component

Input field with validation and theming.

```typescript
<TextField
  label="Client Name"
  value={clientName}
  onChangeText={setClientName}
  placeholder="Enter client name"
  required
  error={errors.clientName}
/>
```

### Screen Components

#### Dashboard
- Real-time statistics
- Recent activity feed
- Quick action buttons
- Navigation to other sections

#### Estimates
- List view with filtering
- Create/edit estimate wizard
- PDF generation (planned)
- Status management

#### Clients
- Client directory
- Contact information management
- Project history
- Communication tracking

#### Templates
- Reusable estimate templates
- Category organization
- Template creation wizard
- Import/export functionality

## 🚀 Deployment

### Building for Production

#### Web Deployment

```bash
# Build for web
npm run build:web

# The build output will be in the 'dist' directory
# Deploy to your preferred hosting service (Vercel, Netlify, etc.)
```

#### Mobile App Deployment

```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Configure EAS
eas build:configure

# Build for iOS
eas build --platform ios

# Build for Android
eas build --platform android

# Submit to app stores
eas submit --platform ios
eas submit --platform android
```

### Environment Configuration

Create environment-specific configuration files:

- `.env.development` - Development environment
- `.env.staging` - Staging environment
- `.env.production` - Production environment

### Continuous Integration

Example GitHub Actions workflow:

```yaml
name: CI/CD
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install
      - run: npm run lint
      - run: npx tsc --noEmit
      - run: npm test
```

## 🤝 Contributing

We welcome contributions to the Construction Estimate App! Please follow these guidelines:

### Development Process

1. **Fork the Repository**
   ```bash
   git fork https://github.com/yourusername/construction-estimate-app.git
   ```

2. **Create a Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make Your Changes**
   - Follow the existing code style
   - Add tests for new functionality
   - Update documentation as needed

4. **Commit Your Changes**
   ```bash
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create Pull Request**
   ```bash
   git push origin feature/your-feature-name
   ```

### Code Style Guidelines

- Use TypeScript for all new code
- Follow React Native best practices
- Use meaningful variable and function names
- Add JSDoc comments for complex functions
- Maintain consistent indentation (2 spaces)

### Commit Message Convention

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Test additions or modifications
- `chore:` - Maintenance tasks

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Expo](https://expo.dev/) - For the amazing React Native framework
- [Supabase](https://supabase.com/) - For the backend infrastructure
- [Lucide](https://lucide.dev/) - For the beautiful icons
- [React Navigation](https://reactnavigation.org/) - For navigation solutions

## 📞 Support

If you have any questions or need help with the app:

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/construction-estimate)
- 📖 Documentation: [Full documentation](https://docs.constructionestimate.app)
- 🐛 Issues: [GitHub Issues](https://github.com/yourusername/construction-estimate-app/issues)

## 🗺️ Roadmap

### Upcoming Features

- [ ] PDF export for estimates
- [ ] Email integration for sending estimates
- [ ] Advanced reporting and analytics
- [ ] Mobile app push notifications
- [ ] Offline mode support
- [ ] Multi-language support
- [ ] Advanced user permissions
- [ ] Integration with accounting software
- [ ] Photo attachments for estimates
- [ ] Digital signature collection

### Version History

- **v1.0.0** - Initial release with core functionality
- **v1.1.0** - Enhanced UI and bug fixes (planned)
- **v1.2.0** - PDF export and email features (planned)
- **v2.0.0** - Advanced features and integrations (planned)

---

**Built with ❤️ for the construction industry**
