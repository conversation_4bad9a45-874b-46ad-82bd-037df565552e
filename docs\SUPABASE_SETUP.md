# Supabase Setup Guide

This guide will help you set up Supabase for the Construction Estimate App.

## Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- Node.js and npm installed
- The Construction Estimate App cloned locally

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: Construction Estimate App
   - **Database Password**: Choose a strong password (save this!)
   - **Region**: Choose the region closest to your users
5. Click "Create new project"
6. Wait for the project to be created (this takes a few minutes)

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project.supabase.co`)
   - **Anon public key** (starts with `eyJ...`)

## Step 3: Configure Environment Variables

1. In your project root, create a `.env` file:
   ```bash
   touch .env
   ```

2. Add your Supabase credentials:
   ```env
   EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
   ```

   Replace the values with your actual Supabase URL and anon key.

## Step 4: Run Database Migrations

The app includes a migration file that sets up all the necessary tables and security policies.

### Option A: Using Supabase Dashboard (Recommended for beginners)

1. Go to your Supabase dashboard
2. Navigate to **SQL Editor**
3. Click "New query"
4. Copy the contents of `supabase/migrations/20250524020959_delicate_summit.sql`
5. Paste it into the SQL editor
6. Click "Run" to execute the migration

### Option B: Using Supabase CLI (For advanced users)

1. Install Supabase CLI:
   ```bash
   npm install -g supabase
   ```

2. Login to Supabase:
   ```bash
   supabase login
   ```

3. Link your project:
   ```bash
   supabase link --project-ref your-project-ref
   ```
   (Find your project ref in the Supabase dashboard URL)

4. Push the migration:
   ```bash
   supabase db push
   ```

## Step 5: Set Up Authentication

1. In your Supabase dashboard, go to **Authentication** → **Settings**
2. Configure the following:
   - **Site URL**: `http://localhost:8081` (for development)
   - **Redirect URLs**: Add your app's URLs
3. Enable email authentication (it's enabled by default)

## Step 6: Create Test Users (Optional)

You can create test users directly in the Supabase dashboard:

1. Go to **Authentication** → **Users**
2. Click "Add user"
3. Create users with different roles:
   - **Admin**: <EMAIL>
   - **Manager**: <EMAIL>
   - **Employee**: <EMAIL>
   - **Client**: <EMAIL>

After creating users in the auth system, you'll need to add their profiles to the `users` table:

```sql
-- Run this in the SQL Editor for each user
INSERT INTO users (id, email, first_name, last_name, role)
VALUES (
  'user-auth-id-from-auth-users-table',
  '<EMAIL>',
  'Admin',
  'User',
  'admin'
);
```

## Step 7: Test the Connection

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Try to register a new user or login with existing credentials
3. Check the Supabase dashboard to see if data is being created

## Troubleshooting

### Common Issues

1. **"Invalid API key" error**
   - Double-check your environment variables
   - Make sure you're using the anon key, not the service key
   - Restart your development server after changing .env

2. **"Row Level Security" errors**
   - Make sure the migration ran successfully
   - Check that RLS policies are created in the Supabase dashboard
   - Verify user roles are set correctly

3. **Connection timeout**
   - Check your internet connection
   - Verify the Supabase URL is correct
   - Try accessing the Supabase dashboard directly

### Checking Your Setup

Run this test query in the Supabase SQL Editor to verify everything is working:

```sql
-- Check if tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'clients', 'estimates', 'estimate_items', 'templates', 'template_items');

-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND rowsecurity = true;
```

You should see all 6 tables listed and RLS enabled for all of them.

## Next Steps

Once Supabase is set up:

1. **Test Authentication**: Try registering and logging in
2. **Create Sample Data**: Add some clients and estimates
3. **Explore the Dashboard**: See real data in the app
4. **Set Up Production**: When ready, create a production Supabase project

## Security Notes

- Never commit your `.env` file to version control
- Use different Supabase projects for development and production
- Regularly rotate your database password
- Monitor your Supabase usage and set up billing alerts

## Support

If you encounter issues:

1. Check the [Supabase documentation](https://supabase.com/docs)
2. Look at the browser console for error messages
3. Check the Supabase dashboard logs
4. Review the app's error handling in the console

The app includes comprehensive error handling and logging to help diagnose issues.
